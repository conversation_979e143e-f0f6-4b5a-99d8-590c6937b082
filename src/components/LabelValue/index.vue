<script lang="ts" setup>
defineOptions({
  name: 'LabelValue',
})

const props = withDefaults(defineProps<Props>(), {
  label: '',
  value: '',
})

// 定义 props
interface Props {
  label?: string
  value?: string
}
</script>

<template>
  <view class="label-value">
    <view class="label">
      <slot name="label">
        {{ props.label }}
      </slot>
    </view>
    ：
    <view class="value">
      <slot name="value">
        {{ props.value }}
      </slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
//
</style>
